import { Component, Input, Output, EventEmitter } from '@angular/core';
import { UserProfile } from '../../../../core/services/auth.service';

@Component({
  selector: 'app-profile-settings',
  standalone: false,
  templateUrl: './profile-settings.component.html',
  styleUrl: './profile-settings.component.css'
})
export class ProfileSettingsComponent {
  @Input() userProfile: UserProfile | null = null;
  @Output() refreshProfile = new EventEmitter<void>();

  onRefreshProfile(): void {
    this.refreshProfile.emit();
  }
}
