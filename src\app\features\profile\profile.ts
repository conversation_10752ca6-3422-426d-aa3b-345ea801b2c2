import { Component, OnInit } from '@angular/core';
import { AuthService, UserProfile } from '../../core/services/auth.service';
import { GameService } from '../../core/services/game.service';
import { UserService } from '../../core/services/user.service';
import { Router } from '@angular/router';
import { Game } from '../../core/models/game.model';
import { User, UserFilters } from '../../core/models/user.model';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.html',
  styleUrl: './profile.css'
})
export class Profile implements OnInit {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  // Admin section management
  activeSection: 'profile' | 'users' | 'games' = 'profile';

  // Games management
  games: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  // Users management
  users: User[] = [];
  usersLoading = false;
  usersError = '';
  totalUsers = 0;
  currentPage = 1;
  pageSize = 10;

  // Search and filtering
  searchTerm = '';
  selectedRole: 'all' | 'staff' | 'user' | 'superuser' = 'all';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  sortBy: 'date_joined' | '-date_joined' | 'email' | '-email' | 'username' | '-username' = '-date_joined';

  constructor(
    private authService: AuthService,
    private gameService: GameService,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  refreshProfile(): void {
    this.loadUserProfile();
  }

  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        alert('Token is valid!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        alert('Token verification failed: ' + error.message);
      }
    });
  }

  // Admin section management
  setActiveSection(section: 'profile' | 'users' | 'games'): void {
    this.activeSection = section;

    if (section === 'games' && this.games.length === 0) {
      this.loadGames();
    } else if (section === 'users' && this.users.length === 0) {
      this.loadUsers();
    }
  }

  // Games management methods
  loadGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    this.gameService.getGames().subscribe({
      next: (games) => {
        this.games = games;
        this.gamesLoading = false;
      },
      error: (error) => {
        this.gamesError = error.message || 'Failed to load games';
        this.gamesLoading = false;
      }
    });
  }

  deleteGame(gameId: number): void {
    if (confirm('Вы уверены, что хотите удалить эту игру?')) {
      this.gameService.deleteGame(gameId).subscribe({
        next: () => {
          this.games = this.games.filter(game => game.id !== gameId);
          alert('Игра успешно удалена');
        },
        error: (error) => {
          alert('Ошибка при удалении игры: ' + error.message);
        }
      });
    }
  }

  // Users management methods
  loadUsers(): void {
    this.usersLoading = true;
    this.usersError = '';

    const filters = this.buildUserFilters();

    this.userService.getUsers(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.users = response.results;
        this.totalUsers = response.count;
        this.usersLoading = false;
      },
      error: (error) => {
        this.usersError = error.message || 'Failed to load users';
        this.usersLoading = false;
      }
    });
  }

  private buildUserFilters(): UserFilters {
    const filters: UserFilters = {
      ordering: this.sortBy
    };

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.selectedRole !== 'all') {
      if (this.selectedRole === 'staff') {
        filters.is_staff = true;
      } else if (this.selectedRole === 'user') {
        filters.is_staff = false;
      } else if (this.selectedRole === 'superuser') {
        filters.is_superuser = true;
      }
    }

    if (this.selectedStatus !== 'all') {
      filters.is_active = this.selectedStatus === 'active';
    }

    return filters;
  }

  // Search and filter methods
  onSearchChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onRoleFilterChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadUsers();
  }

  // Pagination methods
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadUsers();
  }

  get totalPages(): number {
    return Math.ceil(this.totalUsers / this.pageSize);
  }

  get pages(): number[] {
    const pages = [];
    for (let i = 1; i <= this.totalPages; i++) {
      pages.push(i);
    }
    return pages;
  }

  // User management actions
  toggleUserStatus(user: User): void {
    this.userService.toggleUserStatus(user.id, !user.is_active).subscribe({
      next: (updatedUser) => {
        const index = this.users.findIndex(u => u.id === user.id);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
      },
      error: (error) => {
        console.error('Failed to update user status:', error);
        alert('Failed to update user status: ' + error.message);
      }
    });
  }

  toggleStaffStatus(user: User): void {
    this.userService.toggleStaffStatus(user.id, !user.is_staff).subscribe({
      next: (updatedUser) => {
        const index = this.users.findIndex(u => u.id === user.id);
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
      },
      error: (error) => {
        console.error('Failed to update staff status:', error);
        alert('Failed to update staff status: ' + error.message);
      }
    });
  }

  deleteUser(user: User): void {
    if (confirm(`Are you sure you want to delete user ${user.email}?`)) {
      this.userService.deleteUser(user.id).subscribe({
        next: () => {
          this.users = this.users.filter(u => u.id !== user.id);
          this.totalUsers--;
        },
        error: (error) => {
          console.error('Failed to delete user:', error);
          alert('Failed to delete user: ' + error.message);
        }
      });
    }
  }

  // Utility methods
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getUserRoleText(user: User): string {
    if (user.is_superuser) return 'Суперпользователь';
    if (user.is_staff) return 'Администратор';
    return 'Пользователь';
  }

  getUserRoleClass(user: User): string {
    if (user.is_superuser) return 'text-red-400';
    if (user.is_staff) return 'text-green-400';
    return 'text-blue-400';
  }
}
