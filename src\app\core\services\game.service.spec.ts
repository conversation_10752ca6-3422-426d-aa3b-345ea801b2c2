import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { GameService } from './game.service';
import { Game, CreateGameRequest, GameFilters, GameListResponse } from '../models/game.model';
import { environment } from '../../environments/environment';

describe('GameService', () => {
  let service: GameService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/api/games`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [GameService]
    });
    service = TestBed.inject(GameService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getGames', () => {
    it('should fetch games without filters', () => {
      const mockResponse: GameListResponse = {
        count: 1,
        next: null,
        previous: null,
        results: [
          {
            id: 1,
            title: 'Test Game',
            description: 'Test Description',
            requires_device: true,
            price: '1990.00',
            trial_available: true,
            cover_image: '/media/test.jpg',
            created_at: '2025-01-01T00:00:00Z'
          }
        ]
      };

      service.getGames().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('GET');
      expect(req.request.params.keys().length).toBe(0);
      req.flush(mockResponse);
    });

    it('should fetch games with filters and pagination', () => {
      const filters: GameFilters = {
        search: 'test',
        ordering: '-created_at'
      };

      service.getGames(filters, 2, 10).subscribe();

      const req = httpMock.expectOne(request =>
        request.url === `${apiUrl}/` &&
        request.params.get('search') === 'test' &&
        request.params.get('ordering') === '-created_at' &&
        request.params.get('page') === '2' &&
        request.params.get('page_size') === '10'
      );
      expect(req.request.method).toBe('GET');
      req.flush({ count: 0, next: null, previous: null, results: [] });
    });
  });

  describe('getGame', () => {
    it('should fetch a single game', () => {
      const mockGame: Game = {
        id: 1,
        title: 'Test Game',
        description: 'Test Description',
        requires_device: true,
        price: '1990.00',
        trial_available: true,
        cover_image: '/media/test.jpg',
        created_at: '2025-01-01T00:00:00Z'
      };

      service.getGame(1).subscribe(game => {
        expect(game).toEqual(mockGame);
      });

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGame);
    });
  });

  describe('createGame', () => {
    it('should create a game with JSON data', () => {
      const gameData: CreateGameRequest = {
        title: 'New Game',
        description: 'New Description',
        requires_device: false,
        price: '2990.00',
        trial_available: true
      };

      const mockResponse: Game = {
        id: 2,
        ...gameData,
        cover_image: null,
        created_at: '2025-01-01T00:00:00Z'
      };

      service.createGame(gameData).subscribe(game => {
        expect(game).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(gameData);
      req.flush(mockResponse);
    });

    it('should create a game with FormData when file is included', () => {
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const gameData: CreateGameRequest = {
        title: 'New Game',
        description: 'New Description',
        requires_device: false,
        price: '2990.00',
        trial_available: true,
        cover_image: file
      };

      service.createGame(gameData).subscribe();

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body instanceof FormData).toBeTruthy();
      req.flush({});
    });
  });

  describe('updateGame', () => {
    it('should update a game', () => {
      const gameData = { title: 'Updated Game' };
      const mockResponse: Game = {
        id: 1,
        title: 'Updated Game',
        description: 'Test Description',
        requires_device: true,
        price: '1990.00',
        trial_available: true,
        cover_image: null,
        created_at: '2025-01-01T00:00:00Z'
      };

      service.updateGame(1, gameData).subscribe(game => {
        expect(game).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('PATCH');
      req.flush(mockResponse);
    });
  });

  describe('deleteGame', () => {
    it('should delete a game', () => {
      service.deleteGame(1).subscribe();

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('error handling', () => {
    it('should handle 401 errors', () => {
      service.getGames().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.message).toBe('Unauthorized. Please log in.');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
    });

    it('should handle 403 errors', () => {
      service.createGame({} as CreateGameRequest).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.message).toBe('Forbidden. Staff access required.');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush('Forbidden', { status: 403, statusText: 'Forbidden' });
    });

    it('should preserve 400 error structure', () => {
      const errorResponse = {
        title: ['This field is required.'],
        price: ['Invalid price format.']
      };

      service.createGame({} as CreateGameRequest).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.error).toEqual(errorResponse);
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush(errorResponse, { status: 400, statusText: 'Bad Request' });
    });
  });
});
