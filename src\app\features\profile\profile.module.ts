import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { FormsModule } from "@angular/forms";
import { Profile } from './profile';
import { SharedModule } from "../../shared/shared.module";
import { ProfileSettingsComponent } from './components/profile-settings/profile-settings.component';
import { UsersManagementComponent } from './components/users-management/users-management.component';
import { GamesManagementComponent } from './components/games-management/games-management.component';

@NgModule({
    declarations: [
        Profile,
        ProfileSettingsComponent,
        UsersManagementComponent,
        GamesManagementComponent
    ],
    imports: [
        CommonModule,
        RouterModule,
        FormsModule,
        SharedModule
    ]
})
export class ProfileModule { }
