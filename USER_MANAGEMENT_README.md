# User Management System

## Overview
A comprehensive user management system for the admin panel with real-time search, filtering, and table-based display.

## Features

### 🔍 Search & Filtering
- **Search**: Real-time search by email and username
- **Role Filter**: Filter by user roles (All, Superusers, Administrators, Users)
- **Status Filter**: Filter by account status (All, Active, Inactive)
- **Sorting**: Sort by registration date, email, or username (ascending/descending)

### 📊 Table Display
- **Comprehensive User Information**:
  - User ID
  - Username with avatar
  - Email address
  - Role (Superuser/Administrator/User)
  - Account status (Active/Inactive)
  - Registration date
  - Last login date
  - Action buttons

### ⚡ User Management Actions
- **Toggle User Status**: Activate/Deactivate user accounts
- **Toggle Staff Status**: Grant/Remove administrator privileges (Superuser only)
- **Delete Users**: Remove users from the system (Superuser only, cannot delete self)

### 📄 Pagination
- **Page Navigation**: Navigate through multiple pages of users
- **Results Summary**: Shows current page, total pages, and total user count
- **Configurable Page Size**: Default 10 users per page

## API Integration

### Endpoints Used
```
GET /api/users/
- Query Parameters:
  - search: Search term for email/username
  - is_staff: Filter by staff status (true/false)
  - is_active: Filter by active status (true/false)
  - is_superuser: Filter by superuser status (true/false)
  - ordering: Sort field (-date_joined, date_joined, email, -email, username, -username)
  - page: Page number
  - page_size: Number of results per page

PATCH /api/users/{id}/
- Update user properties (staff only)

DELETE /api/users/{id}/
- Delete user (superuser only)
```

### Sample API Response
```json
{
  "count": 8,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "is_active": true,
      "is_staff": true,
      "is_superuser": true,
      "date_joined": "2024-01-01T10:00:00Z",
      "last_login": "2025-06-27T08:30:00Z"
    }
  ]
}
```

## User Interface

### Access
- Available in the Profile page under "Управление пользователями" (User Management)
- Only visible to staff users (is_staff = true)

### Design Features
- **Dark Theme**: Consistent with the application's dark gradient design
- **Responsive Layout**: Works on desktop and mobile devices
- **Loading States**: Shows loading spinners during API calls
- **Error Handling**: Displays error messages with retry options
- **Empty States**: Shows helpful message when no users are found

### Permissions
- **Staff Users**: Can view users, toggle user status
- **Superusers**: Can view users, toggle user status, toggle staff status, delete users
- **Regular Users**: Cannot access user management

## Technical Implementation

### Components
- `Profile Component`: Main container with user management section
- `UserService`: Handles all user-related API calls
- `User Model`: TypeScript interfaces for type safety

### Key Files
- `src/app/core/models/user.model.ts` - User interfaces and types
- `src/app/core/services/user.service.ts` - User API service
- `src/app/features/profile/profile.ts` - Profile component with user management
- `src/app/features/profile/profile.html` - User management UI template

### Sample Data
For testing purposes, the system includes sample data with 8 different user types:
- Superuser admin
- Regular staff members
- Active users
- Inactive users
- Users with different login patterns

## Usage Instructions

1. **Login as Staff User**: Only staff users can access user management
2. **Navigate to Profile**: Go to the profile page
3. **Select User Management**: Click on "Управление пользователями"
4. **Search & Filter**: Use the search and filter controls to find specific users
5. **Manage Users**: Use action buttons to manage user accounts
6. **Navigate Pages**: Use pagination controls for large user lists

## Future Enhancements
- Bulk user operations
- User creation form
- Advanced user details modal
- Export user data functionality
- User activity logs
- Email notifications for user actions
