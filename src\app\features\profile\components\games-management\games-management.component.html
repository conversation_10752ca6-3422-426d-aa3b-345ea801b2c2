<!-- Games Management Content -->
<div class="max-w-6xl">
  <!-- Header -->
  <div class="flex items-center mb-8">
    <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-6">
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
      </svg>
    </div>
    <div>
      <h1 class="text-4xl font-bold text-white mb-2">Управление играми</h1>
      <p class="text-gray-300">Создание, редактирование и удаление игр</p>
    </div>
  </div>

  <!-- Games Management Section -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-8 mb-8">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold text-white">Управление играми</h3>
      <button class="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg shadow-lg hover:from-green-700 hover:to-emerald-700 transition-all transform hover:scale-[1.02]">
        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Добавить игру
      </button>
    </div>

    <!-- Search and Filter Controls -->
    <div class="flex flex-col sm:flex-row gap-4 mb-6">
      <div class="flex-1">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          placeholder="Поиск игр..."
          class="w-full px-4 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
        >
      </div>
      <div class="flex gap-2">
        <select
          [(ngModel)]="sortBy"
          (change)="onSortChange()"
          class="px-4 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:border-blue-500 transition-colors"
        >
          <option value="-created_at">Новые первые</option>
          <option value="created_at">Старые первые</option>
          <option value="title">По названию (А-Я)</option>
          <option value="-title">По названию (Я-А)</option>
          <option value="price">По цене (возр.)</option>
          <option value="-price">По цене (убыв.)</option>
        </select>
        <button
          (click)="clearFilters()"
          class="px-4 py-2 bg-slate-700/60 hover:bg-slate-600/60 text-gray-300 rounded-lg transition-colors"
        >
          Сбросить
        </button>
      </div>
    </div>

    <!-- Loading State -->
    <app-loading-spinner
      *ngIf="gamesLoading"
      size="medium">
    </app-loading-spinner>

    <!-- Error State -->
    <div *ngIf="gamesError && !gamesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <h4 class="text-red-300 font-semibold mb-2">Ошибка загрузки игр</h4>
      <p class="text-red-200 mb-4">{{ gamesError }}</p>
      <button (click)="loadGames()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
        Попробовать снова
      </button>
    </div>

    <!-- Games List -->
    <div *ngIf="!gamesLoading && !gamesError" class="space-y-4">
      <div *ngFor="let game of games" class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-6">
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <h4 class="text-white font-semibold text-lg mb-2">{{ game.title }}</h4>
            <p class="text-gray-300 mb-3">{{ game.description }}</p>
            <div class="flex flex-wrap gap-4 text-sm text-gray-400">
              <span>Цена: <span class="text-green-400 font-medium">${{ game.price }}</span></span>
              <span>Пробная версия: <span [ngClass]="game.trial_available ? 'text-green-400' : 'text-red-400'">{{ game.trial_available ? 'Доступна' : 'Недоступна' }}</span></span>
              <span>Создано: <span class="text-blue-400">{{ game.created_at | date:'short' }}</span></span>
            </div>
          </div>
          <div class="flex space-x-2 ml-4">
            <button class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors">
              Редактировать
            </button>
            <button (click)="deleteGame(game.id)" class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors">
              Удалить
            </button>
          </div>
        </div>
      </div>

      <div *ngIf="games.length === 0" class="text-center py-8 text-gray-400">
        Игры не найдены
      </div>
    </div>
  </div>
</div>
