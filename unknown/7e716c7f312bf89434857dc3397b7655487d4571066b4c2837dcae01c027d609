import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface LoadingState {
  [key: string]: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class LoadingService {
  private loadingSubject = new BehaviorSubject<LoadingState>({});
  public loading$ = this.loadingSubject.asObservable();

  private globalLoadingSubject = new BehaviorSubject<boolean>(false);
  public globalLoading$ = this.globalLoadingSubject.asObservable();

  constructor() {}

  /**
   * Set loading state for a specific key
   */
  setLoading(key: string, loading: boolean): void {
    const currentState = this.loadingSubject.value;
    this.loadingSubject.next({
      ...currentState,
      [key]: loading
    });
  }

  /**
   * Get loading state for a specific key
   */
  getLoading(key: string): Observable<boolean> {
    return new Observable(observer => {
      this.loading$.subscribe(state => {
        observer.next(!!state[key]);
      });
    });
  }

  /**
   * Check if any loading is active
   */
  isAnyLoading(): Observable<boolean> {
    return new Observable(observer => {
      this.loading$.subscribe(state => {
        const hasLoading = Object.values(state).some(loading => loading);
        observer.next(hasLoading);
      });
    });
  }

  /**
   * Set global loading state (for page transitions)
   */
  setGlobalLoading(loading: boolean): void {
    this.globalLoadingSubject.next(loading);
  }

  /**
   * Clear all loading states
   */
  clearAll(): void {
    this.loadingSubject.next({});
    this.globalLoadingSubject.next(false);
  }

  /**
   * Clear specific loading state
   */
  clear(key: string): void {
    const currentState = this.loadingSubject.value;
    const newState = { ...currentState };
    delete newState[key];
    this.loadingSubject.next(newState);
  }
}
