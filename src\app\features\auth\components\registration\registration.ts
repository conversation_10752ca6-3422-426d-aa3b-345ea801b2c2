import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../../core/services/auth.service';
import { LoadingService } from '../../../../core/services/loading.service';

@Component({
  selector: 'app-registration',
  standalone: false,
  templateUrl: './registration.html',
  styleUrl: './registration.css'
})
export class Registration implements OnInit, OnDestroy {
  registrationForm: FormGroup;
  verificationForm: FormGroup;
  showCodeField = false;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  registeredEmail = '';
  fieldErrors: { [key: string]: string } = {};

  private registrationSubscription?: Subscription;
  private verificationSubscription?: Subscription;
  private resendSubscription?: Subscription;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private loadingService: LoadingService
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    this.verificationForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  ngOnInit(): void {
    // Subscribe to different loading states
    this.registrationSubscription = this.loadingService.getLoading('registration').subscribe(
      loading => {
        if (!this.showCodeField) {
          this.isLoading = loading;
        }
      }
    );

    this.verificationSubscription = this.loadingService.getLoading('verification').subscribe(
      loading => {
        if (this.showCodeField) {
          this.isLoading = loading;
        }
      }
    );

    this.resendSubscription = this.loadingService.getLoading('resend-code').subscribe(
      loading => {
        if (this.showCodeField) {
          this.isLoading = loading;
        }
      }
    );


  }

  ngOnDestroy(): void {
    this.registrationSubscription?.unsubscribe();
    this.verificationSubscription?.unsubscribe();
    this.resendSubscription?.unsubscribe();
  }

  onRegisterClick() {
    if (this.registrationForm.valid) {
      this.errorMessage = '';
      this.clearFieldErrors();

      const credentials = this.registrationForm.value;

      this.authService.register(credentials).subscribe({
        next: (response) => {
          this.registeredEmail = response.email;
          this.showCodeField = true;
          this.successMessage = ''; // Clear success message to avoid duplication
        },
        error: (error) => {
          this.handleRegistrationError(error);
        }
      });
    } else {
      this.markFormGroupTouched(this.registrationForm);
    }
  }

  onVerifyCode() {
    if (this.verificationForm.valid) {
      this.errorMessage = '';

      const verificationData = {
        email: this.registeredEmail,
        code: this.verificationForm.value.code
      };

      this.authService.verifyCode(verificationData).subscribe({
        next: (response) => {
          this.successMessage = response.detail;
          // Redirect to login after successful verification
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2000);
        },
        error: (error) => {
          this.handleVerificationError(error);
        }
      });
    } else {
      this.markFormGroupTouched(this.verificationForm);
    }
  }

  resendCode() {
    // Use the dedicated resend-code endpoint
    const resendData = {
      email: this.registeredEmail
    };

    this.errorMessage = '';

    this.authService.resendCode(resendData).subscribe({
      next: (response) => {
        this.successMessage = response.detail;
        this.verificationForm.reset();
      },
      error: (error) => {
        this.handleResendCodeError(error);
      }
    });
  }

  getFieldError(fieldName: string, formGroup: FormGroup = this.registrationForm): string {
    // First check for API field errors
    if (this.fieldErrors[fieldName]) {
      return this.fieldErrors[fieldName];
    }

    // Then check for form validation errors
    const field = formGroup.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return 'Это поле обязательно';
      }
      if (field.errors['email']) {
        return 'Введите корректный email';
      }
      if (field.errors['minlength']) {
        return `Минимум ${field.errors['minlength'].requiredLength} символов`;
      }
      if (field.errors['pattern']) {
        return 'Код должен содержать 6 цифр';
      }
    }
    return '';
  }

  isFieldInvalid(fieldName: string, formGroup: FormGroup = this.registrationForm): boolean {
    // Check for API field errors first
    if (this.fieldErrors[fieldName]) {
      return true;
    }

    // Then check for form validation errors
    const field = formGroup.get(fieldName);
    return !!(field && field.errors && field.touched);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  private handleRegistrationError(error: any) {
    this.clearFieldErrors();

    if (error.error) {
      // Handle field-specific errors (e.g., email already exists)
      if (error.error.email && error.error.email.length > 0) {
        this.fieldErrors['email'] = error.error.email[0];
      }
      if (error.error.password && error.error.password.length > 0) {
        this.fieldErrors['password'] = error.error.password[0];
      }

      // Handle general errors
      if (error.error.non_field_errors && error.error.non_field_errors.length > 0) {
        this.errorMessage = error.error.non_field_errors[0];
      }

      // If no specific errors were found, show a generic message
      if (!this.fieldErrors['email'] && !this.fieldErrors['password'] && !this.errorMessage) {
        this.errorMessage = 'Ошибка при регистрации';
      }
    } else {
      this.errorMessage = error.message || 'Ошибка при регистрации';
    }
  }

  private handleVerificationError(error: any) {
    if (error.error && error.error.non_field_errors && error.error.non_field_errors.length > 0) {
      // Handle specific verification errors like "Incorrect code" or "User not found"
      this.errorMessage = error.error.non_field_errors[0];
    } else {
      this.errorMessage = error.message || 'Ошибка при проверке кода';
    }
  }

  private handleResendCodeError(error: any) {
    this.clearFieldErrors();

    if (error.error) {
      // Handle field-specific errors (e.g., email not found, already verified)
      if (error.error.email && error.error.email.length > 0) {
        this.errorMessage = error.error.email[0];
      } else if (error.error.non_field_errors && error.error.non_field_errors.length > 0) {
        this.errorMessage = error.error.non_field_errors[0];
      } else {
        this.errorMessage = 'Ошибка при повторной отправке кода';
      }
    } else {
      this.errorMessage = error.message || 'Ошибка при повторной отправке кода';
    }
  }

  private clearFieldErrors() {
    this.fieldErrors = {};
  }
}
