/* Profile Settings specific styles */
.profile-section {
  transition: all 0.3s ease;
}

.profile-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.profile-input {
  transition: all 0.3s ease;
}

.profile-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.profile-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.profile-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.profile-button:hover::before {
  left: 100%;
}
