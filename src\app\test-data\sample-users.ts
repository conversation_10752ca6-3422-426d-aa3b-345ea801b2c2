import { User } from '../core/models/user.model';

export const SAMPLE_USERS: User[] = [
  {
    id: 1,
    username: "admin",
    email: "<EMAIL>",
    is_active: true,
    is_staff: true,
    is_superuser: true,
    date_joined: "2024-01-01T10:00:00Z",
    last_login: "2025-06-27T08:30:00Z"
  },
  {
    id: 2,
    username: "user1",
    email: "<EMAIL>",
    is_active: true,
    is_staff: false,
    is_superuser: false,
    date_joined: "2024-02-15T12:30:00Z",
    last_login: null
  },
  {
    id: 3,
    username: "moderator",
    email: "<EMAIL>",
    is_active: true,
    is_staff: true,
    is_superuser: false,
    date_joined: "2024-03-10T09:15:00Z",
    last_login: "2025-06-26T14:20:00Z"
  },
  {
    id: 4,
    username: "inactive_user",
    email: "<EMAIL>",
    is_active: false,
    is_staff: false,
    is_superuser: false,
    date_joined: "2024-04-05T16:45:00Z",
    last_login: "2024-05-01T10:30:00Z"
  },
  {
    id: 5,
    username: "john_doe",
    email: "<EMAIL>",
    is_active: true,
    is_staff: false,
    is_superuser: false,
    date_joined: "2024-05-20T11:00:00Z",
    last_login: "2025-06-25T16:45:00Z"
  },
  {
    id: 6,
    username: "jane_smith",
    email: "<EMAIL>",
    is_active: true,
    is_staff: false,
    is_superuser: false,
    date_joined: "2024-06-01T13:30:00Z",
    last_login: "2025-06-27T09:15:00Z"
  },
  {
    id: 7,
    username: "support_admin",
    email: "<EMAIL>",
    is_active: true,
    is_staff: true,
    is_superuser: false,
    date_joined: "2024-01-15T08:00:00Z",
    last_login: "2025-06-26T17:30:00Z"
  },
  {
    id: 8,
    username: "test_user",
    email: "<EMAIL>",
    is_active: false,
    is_staff: false,
    is_superuser: false,
    date_joined: "2024-07-01T14:20:00Z",
    last_login: null
  }
];
