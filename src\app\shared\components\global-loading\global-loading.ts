import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';
import { Subscription } from 'rxjs';
import { LoadingService } from '../../../core/services/loading.service';

@Component({
  selector: 'app-global-loading',
  standalone: false,
  templateUrl: './global-loading.html',
  styleUrl: './global-loading.css'
})
export class GlobalLoading implements OnInit, OnDestroy {
  isLoading = false;
  private routerSubscription?: Subscription;
  private loadingSubscription?: Subscription;
  private currentUrl = '';

  constructor(
    private router: Router,
    private loadingService: LoadingService
  ) {}

  ngOnInit(): void {
    this.currentUrl = this.router.url;

    // Subscribe to router events for page transitions
    this.routerSubscription = this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        // Only show loading for actual route changes, not anchor links
        const newUrl = event.url.split('#')[0]; // Remove fragment
        const currentUrlBase = this.currentUrl.split('#')[0];

        if (newUrl !== currentUrlBase) {
          this.loadingService.setGlobalLoading(true);
        }
      } else if (
        event instanceof NavigationEnd ||
        event instanceof NavigationCancel ||
        event instanceof NavigationError
      ) {
        this.currentUrl = event instanceof NavigationEnd ? event.url : this.currentUrl;
        // Add a small delay to prevent flashing
        setTimeout(() => {
          this.loadingService.setGlobalLoading(false);
        }, 200);
      }
    });

    // Subscribe to global loading state
    this.loadingSubscription = this.loadingService.globalLoading$.subscribe(
      loading => this.isLoading = loading
    );
  }

  ngOnDestroy(): void {
    this.routerSubscription?.unsubscribe();
    this.loadingSubscription?.unsubscribe();
  }
}
