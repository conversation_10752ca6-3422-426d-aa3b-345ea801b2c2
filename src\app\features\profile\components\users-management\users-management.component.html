<!-- Users Management Content -->
<div class="max-w-6xl">
  <!-- Header -->
  <div class="flex items-center mb-8">
    <div class="w-16 h-16 bg-gradient-to-br from-blue-600 to-slate-700 rounded-full flex items-center justify-center mr-6">
      <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
      </svg>
    </div>
    <div>
      <h1 class="text-4xl font-bold text-white mb-2">Управление пользователями</h1>
      <p class="text-gray-300">Просмотр и управление пользователями системы</p>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Search Input -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Поиск</label>
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (ngModelChange)="onSearchChange()"
          placeholder="Email, имя пользователя..."
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
      </div>

      <!-- Role Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Роль</label>
        <select
          [(ngModel)]="selectedRole"
          (ngModelChange)="onRoleFilterChange()"
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">Все роли</option>
          <option value="superuser">Суперпользователи</option>
          <option value="staff">Администраторы</option>
          <option value="user">Пользователи</option>
        </select>
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Статус</label>
        <select
          [(ngModel)]="selectedStatus"
          (ngModelChange)="onStatusFilterChange()"
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">Все статусы</option>
          <option value="active">Активные</option>
          <option value="inactive">Неактивные</option>
        </select>
      </div>

      <!-- Sort By -->
      <div>
        <label class="block text-sm font-medium text-gray-300 mb-2">Сортировка</label>
        <select
          [(ngModel)]="sortBy"
          (ngModelChange)="onSortChange()"
          class="w-full px-3 py-2 bg-slate-800/60 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="-date_joined">Дата регистрации (новые)</option>
          <option value="date_joined">Дата регистрации (старые)</option>
          <option value="email">Email (А-Я)</option>
          <option value="-email">Email (Я-А)</option>
          <option value="username">Имя (А-Я)</option>
          <option value="-username">Имя (Я-А)</option>
        </select>
      </div>
    </div>

    <!-- Results Summary -->
    <div class="mt-4 flex justify-between items-center text-sm text-gray-400">
      <span>Найдено пользователей: {{ totalUsers }}</span>
      <button
        (click)="loadUsers()"
        class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
      >
        Обновить
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="usersLoading" class="flex justify-center py-8">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Error State -->
  <div *ngIf="usersError && !usersLoading" class="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
    <p class="text-red-400">{{ usersError }}</p>
    <button (click)="loadUsers()" class="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors">
      Повторить попытку
    </button>
  </div>

  <!-- Users Table -->
  <div *ngIf="!usersLoading && !usersError" class="bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg overflow-hidden">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-slate-800/60">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th> 
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Email</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Роль</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Статус</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Дата регистрации</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Последний вход</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Действия</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-slate-700/50">
          <tr *ngFor="let user of users" class="hover:bg-slate-700/20 transition-colors">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ user.id }}</td> 
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ user.email }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [ngClass]="getUserRoleClass(user)" class="text-sm font-medium">
                {{ getUserRoleText(user) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span [ngClass]="user.is_active ? 'bg-green-900/50 text-green-400 border-green-500/50' : 'bg-red-900/50 text-red-400 border-red-500/50'"
                    class="inline-flex px-2 py-1 text-xs font-semibold rounded-full border">
                {{ user.is_active ? 'Активен' : 'Неактивен' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ formatDate(user.date_joined) }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
              {{ user.last_login ? formatDate(user.last_login) : 'Никогда' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="relative inline-block text-left">
                <button
                  (click)="toggleDropdown(user.id)"
                  class="inline-flex items-center px-3 py-1.5 bg-slate-700 hover:bg-slate-600 text-gray-300 text-xs rounded-md transition-colors focus:outline-none"
                >
                  Действия
                  <svg class="ml-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </button>

                <div
                  *ngIf="openDropdownId === user.id"
                  class="absolute right-0 mt-1 w-48 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10"
                >
                  <div class="py-1">
                    <button
                      (click)="toggleActiveStatus(user); closeDropdown()"
                      class="w-full text-left px-4 py-2 text-xs text-gray-300 hover:bg-slate-700 hover:text-white transition-colors"
                    >
                      <span [ngClass]="user.is_active ? 'text-red-400' : 'text-green-400'">
                        {{ user.is_active ? '🔴 Деактивировать' : '🟢 Активировать' }}
                      </span>
                    </button>
                    <button
                      (click)="toggleStaffStatus(user); closeDropdown()"
                      class="w-full text-left px-4 py-2 text-xs text-gray-300 hover:bg-slate-700 hover:text-white transition-colors"
                    >
                      <span [ngClass]="user.is_staff ? 'text-orange-400' : 'text-blue-400'">
                        {{ user.is_staff ? '👤 Убрать админа' : '⭐ Сделать админом' }}
                      </span>
                    </button>
                    <div class="border-t border-slate-600 my-1"></div>
                    <button
                      (click)="deleteUser(user); closeDropdown()"
                      class="w-full text-left px-4 py-2 text-xs text-red-400 hover:bg-red-900/20 hover:text-red-300 transition-colors"
                    >
                      🗑️ Удалить пользователя
                    </button>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="bg-slate-800/40 px-6 py-3 border-t border-slate-700/50">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-400">
          Показано {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalUsers) }} из {{ totalUsers }}
        </div>
        <div class="flex space-x-1">
          <!-- Previous Page -->
          <button
            (click)="onPageChange(currentPage - 1)"
            [disabled]="currentPage === 1"
            [ngClass]="currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-700'"
            class="px-3 py-1 bg-slate-800 text-white text-sm rounded transition-colors"
          >
            ←
          </button>

          <!-- Page Numbers -->
          <button
            *ngFor="let page of pages"
            (click)="onPageChange(page)"
            [ngClass]="page === currentPage ? 'bg-blue-600' : 'bg-slate-800 hover:bg-slate-700'"
            class="px-3 py-1 text-white text-sm rounded transition-colors"
          >
            {{ page }}
          </button>

          <!-- Next Page -->
          <button
            (click)="onPageChange(currentPage + 1)"
            [disabled]="currentPage === totalPages"
            [ngClass]="currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-slate-700'"
            class="px-3 py-1 bg-slate-800 text-white text-sm rounded transition-colors"
          >
            →
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="users.length === 0 && !usersLoading" class="text-center py-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-300">Пользователи не найдены</h3>
      <p class="mt-1 text-sm text-gray-400">Попробуйте изменить параметры поиска или фильтры.</p>
    </div>
  </div>
</div>
