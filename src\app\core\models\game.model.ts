export interface Game {
  id: number;
  title: string;
  subtitle?: string | null;
  description: string;
  how_to_play?: string | null;
  target_audience?: string | null;
  price: string;
  trial_available: boolean;
  cover_image?: string | null;
  gallery_images?: string | null;
  system_requirements?: string | null;
  required_equipment?: string | null;
  created_at: string;
}

export interface CreateGameRequest {
  title: string;
  subtitle?: string;
  description: string;
  how_to_play?: string;
  target_audience?: string;
  price: string;
  trial_available: boolean;
  cover_image?: string;
  gallery_images?: string;
  system_requirements?: string;
  required_equipment?: string;
}

export interface UpdateGameRequest extends Partial<CreateGameRequest> {
  id: number;
}
