import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { Game, CreateGameRequest, UpdateGameRequest, GameFilters, GameListResponse } from '../models/game.model';

@Injectable({
  providedIn: 'root'
})
export class GameService {
  private apiUrl = `${environment.apiUrl}/api/games`;

  constructor(private http: HttpClient) {}

  /**
   * Get all games with optional filtering and search
   * @param filters - Optional filters for ordering and search
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  getGames(filters?: GameFilters, page?: number, pageSize?: number): Observable<GameListResponse> {
    let params = new HttpParams();

    if (filters?.ordering) {
      params = params.set('ordering', filters.ordering);
    }

    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    return this.http.get<GameListResponse>(`${this.apiUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get a single game by ID
   * @param id - Game ID
   */
  getGame(id: number): Observable<Game> {
    return this.http.get<Game>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Create a new game (staff only)
   * @param gameData - Game data to create
   */
  createGame(gameData: CreateGameRequest): Observable<Game> {
    const formData = this.prepareFormData(gameData);
    return this.http.post<Game>(`${this.apiUrl}/`, formData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update an existing game (staff only)
   * @param id - Game ID to update
   * @param gameData - Updated game data
   */
  updateGame(id: number, gameData: UpdateGameRequest): Observable<Game> {
    const formData = this.prepareFormData(gameData);
    return this.http.patch<Game>(`${this.apiUrl}/${id}/`, formData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Delete a game (staff only)
   * @param id - Game ID to delete
   */
  deleteGame(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Prepare form data for file uploads
   * @param gameData - Game data that may contain files
   */
  private prepareFormData(gameData: CreateGameRequest | UpdateGameRequest): FormData | any {
    // If cover_image is a File, use FormData for file upload
    if (gameData.cover_image instanceof File) {
      const formData = new FormData();

      Object.keys(gameData).forEach(key => {
        const value = (gameData as any)[key];
        if (value !== undefined && value !== null) {
          if (key === 'gallery_images' && Array.isArray(value)) {
            // Handle gallery images array
            value.forEach((image, index) => {
              formData.append(`gallery_images[${index}]`, image);
            });
          } else {
            formData.append(key, value);
          }
        }
      });

      return formData;
    }

    // Otherwise, send as JSON
    return gameData;
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      return throwError(() => new Error(error.error.message));
    } else {
      // Server-side error - preserve the original error structure for proper handling
      if (error.status === 400) {
        // For 400 errors, preserve the original error structure so components can access field-specific errors
        return throwError(() => error);
      } else if (error.status === 401) {
        return throwError(() => new Error('Unauthorized. Please log in.'));
      } else if (error.status === 403) {
        return throwError(() => new Error('Forbidden. Staff access required.'));
      } else if (error.status === 404) {
        return throwError(() => new Error('Game not found.'));
      } else if (error.status === 500) {
        return throwError(() => new Error('Server error. Please try again later.'));
      } else {
        return throwError(() => new Error(`Error: ${error.message}`));
      }
    }
  }
}
