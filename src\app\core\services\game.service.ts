import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { Game, CreateGameRequest, UpdateGameRequest } from '../models/game.model';

@Injectable({
  providedIn: 'root'
})
export class GameService {
  private apiUrl = `${environment.apiUrl}/api/games`;

  constructor(private http: HttpClient) {}

  /**
   * Get all games
   */
  getGames(): Observable<Game[]> {
    return this.http.get<Game[]>(`${this.apiUrl}/`);
  }

  /**
   * Get a single game by ID
   */
  getGame(id: number): Observable<Game> {
    return this.http.get<Game>(`${this.apiUrl}/${id}/`);
  }

  /**
   * Create a new game (staff only)
   */
  createGame(gameData: CreateGameRequest): Observable<Game> {
    return this.http.post<Game>(`${this.apiUrl}/`, gameData);
  }

  /**
   * Update an existing game (staff only)
   */
  updateGame(id: number, gameData: UpdateGameRequest): Observable<Game> {
    return this.http.put<Game>(`${this.apiUrl}/${id}/`, gameData);
  }

  /**
   * Delete a game (staff only)
   */
  deleteGame(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}/`);
  }
}
