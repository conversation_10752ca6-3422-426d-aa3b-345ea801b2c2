import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpH<PERSON><PERSON>, HttpEvent, HttpInterceptor, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap, finalize } from 'rxjs/operators';
import { LoadingService } from '../services/loading.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  private activeRequests = new Set<string>();

  constructor(private loadingService: LoadingService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    // Generate a unique key for this request
    const requestKey = this.generateRequestKey(request);
    
    // Don't show loading for certain requests (like token refresh)
    if (this.shouldSkipLoading(request)) {
      return next.handle(request);
    }

    // Start loading
    this.activeRequests.add(requestKey);
    this.updateLoadingState(request, true);

    return next.handle(request).pipe(
      tap(event => {
        // Handle successful responses
        if (event instanceof HttpResponse) {
          this.activeRequests.delete(requestKey);
          this.updateLoadingState(request, false);
        }
      }),
      finalize(() => {
        // Ensure loading is stopped even if request fails
        this.activeRequests.delete(requestKey);
        this.updateLoadingState(request, false);
      })
    );
  }

  private generateRequestKey(request: HttpRequest<any>): string {
    return `${request.method}-${request.url}-${Date.now()}`;
  }

  private shouldSkipLoading(request: HttpRequest<any>): boolean {
    // Skip loading for token refresh and verify requests
    const skipUrls = [
      '/api/token/refresh/',
      '/api/token/verify/'
    ];

    return skipUrls.some(url => request.url.includes(url));
  }

  private updateLoadingState(request: HttpRequest<any>, loading: boolean): void {
    // Determine loading key based on request URL
    let loadingKey = 'general';

    if (request.url.includes('/api/auth/')) {
      loadingKey = 'auth';
    } else if (request.url.includes('/api/games/')) {
      loadingKey = 'games';
    } else if (request.url.includes('/api/userprofile/')) {
      loadingKey = 'profile';
    }

    // Set loading state
    this.loadingService.setLoading(loadingKey, loading);

    // Also set specific loading states for different operations
    if (request.url.includes('/api/auth/register/')) {
      this.loadingService.setLoading('registration', loading);
    } else if (request.url.includes('/api/auth/verify-code/')) {
      this.loadingService.setLoading('verification', loading);
    } else if (request.url.includes('/api/auth/resend-code/')) {
      this.loadingService.setLoading('resend-code', loading);
    } else if (request.url.includes('/api/token/') && request.method === 'POST') {
      this.loadingService.setLoading('login', loading);
    }
  }
}
