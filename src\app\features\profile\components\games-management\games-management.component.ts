import { Component, OnInit } from '@angular/core';
import { GameService } from '../../../../core/services/game.service';
import { Game } from '../../../../core/models/game.model';

@Component({
  selector: 'app-games-management',
  standalone: false,
  templateUrl: './games-management.component.html',
  styleUrl: './games-management.component.css'
})
export class GamesManagementComponent implements OnInit {
  games: Game[] = [];
  gamesLoading = false;
  gamesError = '';

  constructor(private gameService: GameService) {}

  ngOnInit(): void {
    this.loadGames();
  }

  loadGames(): void {
    this.gamesLoading = true;
    this.gamesError = '';

    this.gameService.getGames().subscribe({
      next: (games) => {
        this.games = games;
        this.gamesLoading = false;
      },
      error: (error) => {
        this.gamesError = error.message || 'Failed to load games';
        this.gamesLoading = false;
      }
    });
  }

  deleteGame(gameId: number): void {
    if (confirm('Вы уверены, что хотите удалить эту игру?')) {
      this.gameService.deleteGame(gameId).subscribe({
        next: () => {
          this.games = this.games.filter(game => game.id !== gameId);
          alert('Игра успешно удалена');
        },
        error: (error) => {
          alert('Ошибка при удалении игры: ' + error.message);
        }
      });
    }
  }
}
