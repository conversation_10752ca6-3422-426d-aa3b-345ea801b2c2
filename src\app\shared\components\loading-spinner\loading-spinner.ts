import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-loading-spinner',
  standalone: false,
  templateUrl: './loading-spinner.html',
  styleUrl: './loading-spinner.css'
})
export class LoadingSpinner {
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() overlay: boolean = false;
  @Input() fullScreen: boolean = false;

  get spinnerClasses(): string {
    const baseClasses = 'animate-spin text-purple-400';

    switch (this.size) {
      case 'small':
        return `${baseClasses} h-4 w-4`;
      case 'large':
        return `${baseClasses} h-10 w-10`;
      default:
        return `${baseClasses} h-6 w-6`;
    }
  }

  get containerClasses(): string {
    let classes = 'flex items-center justify-center';

    if (this.fullScreen) {
      classes += ' fixed inset-0 z-50 bg-black/40 backdrop-blur-sm';
    } else if (this.overlay) {
      classes += ' absolute inset-0 z-10 bg-black/30 backdrop-blur-sm rounded-lg';
    }

    return classes;
  }

  get spinnerContainerClasses(): string {
    const baseClasses = 'bg-black/60 backdrop-blur-md border border-purple-400/30 rounded-xl shadow-2xl';

    switch (this.size) {
      case 'small':
        return `${baseClasses} p-2`;
      case 'large':
        return `${baseClasses} p-6`;
      default:
        return `${baseClasses} p-4`;
    }
  }
}
